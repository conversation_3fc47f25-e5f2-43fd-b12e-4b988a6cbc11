<div>
    @if (session()->has('message'))
        <flux:callout variant="success" icon="check-circle" class="mb-6">
            {{ session('message') }}
        </flux:callout>
    @endif

    <div class="bg-white shadow-md rounded-lg p-8">
        <form wire:submit="store">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <!-- Property Type -->
            <div>
                <flux:select wire:model.live="property_type" label="Property Type" placeholder="Select Property Type">
                    <option value="">Select Property Type</option>
                    <option value="apartment">Apartment</option>
                    <option value="house">House/Bungalow</option>
                    <option value="land">Land</option>
                    <option value="single_room">Single Room</option>
                </flux:select>
                @error('property_type')
                    <flux:error>{{ $message }}</flux:error>
                @enderror
            </div>

            <!-- Listing Type -->
            <div>
                <flux:radio.group wire:model.live="listing_type" label="Listing Type" variant="segmented">
                    <flux:radio value="for_sale">For Sale</flux:radio>
                    <flux:radio value="for_rent">For Rent</flux:radio>
                </flux:radio.group>
                @error('listing_type')
                    <flux:error>{{ $message }}</flux:error>
                @enderror
            </div>
        </div>

        <!-- Basic Info -->
        <div class="mb-6">
            <h3 class="text-xl font-semibold text-gray-700 mb-4">Basic Information</h3>
            <div class="grid grid-cols-1 gap-6">
                <div>
                    <flux:input wire:model.live="title" id="title" type="text" label="{{ __('Title') }}" class="mt-1 block w-full" placeholder="e.g., Spacious 3-Bedroom Apartment" />
                </div>
                <div>
                    <flux:textarea wire:model.live="description" id="description" label="{{ __('Description') }}" rows="5" class="mt-1 block w-full" placeholder="Describe your property..." />
                </div>
                <div>
                    <flux:input wire:model.live="price" id="price" type="number" step="0.01" label="{{ __('Price') }}" class="mt-1 block w-full" placeholder="e.g., 250000" />
                </div>
            </div>
        </div>

        <!-- Location -->
        <div class="mb-6">
            <h3 class="text-xl font-semibold text-gray-700 mb-4">Location Details</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <flux:input wire:model.live="address_line_1" id="address_line_1" type="text" label="{{ __('Address Line 1') }}" class="mt-1 block w-full" placeholder="e.g., 123 Main St" />
                </div>
                <div>
                    <flux:input wire:model.live="city" id="city" type="text" label="{{ __('City') }}" class="mt-1 block w-full" placeholder="e.g., New York" />
                </div>
                <div>
                    <flux:input wire:model.live="state_region" id="state_region" type="text" label="{{ __('State/Region') }}" class="mt-1 block w-full" placeholder="e.g., NY" />
                </div>
                <div>
                    <flux:input wire:model.live="zip_code" id="zip_code" type="text" label="{{ __('Zip Code') }}" class="mt-1 block w-full" placeholder="e.g., 10001" />
                </div>
                <div>
                    <flux:input wire:model.live="latitude" id="latitude" type="number" step="any" label="{{ __('Latitude') }}" class="mt-1 block w-full" placeholder="e.g., 34.052235" />
                </div>
                <div>
                    <flux:input wire:model.live="longitude" id="longitude" type="number" step="any" label="{{ __('Longitude') }}" class="mt-1 block w-full" placeholder="e.g., -118.243683" />
                </div>
            </div>
            <div class="mt-4">
                <x-input-label :value="__('Set Location on Map')" />
                <div id="mapCreate" style="height: 300px;" class="mt-1 rounded-md border border-gray-300 shadow-sm"></div>
                <p class="mt-1 text-sm text-gray-500">Click on the map to set latitude and longitude.</p>
            </div>
        </div>

        <!-- Dynamic Features -->
        @if (in_array($property_type, ['apartment', 'house', 'single_room']))
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-700 mb-4">Property Features</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <flux:input wire:model.live="bedrooms" id="bedrooms" type="number" label="{{ __('Bedrooms') }}" class="mt-1 block w-full" placeholder="e.g., 3" />
                    </div>
                    <div>
                        <flux:input wire:model.live="bathrooms" id="bathrooms" type="number" label="{{ __('Bathrooms') }}" class="mt-1 block w-full" placeholder="e.g., 2" />
                    </div>
                    <div>
                        <flux:input wire:model.live="square_footage" id="square_footage" type="number" step="0.01" label="{{ __('Square Footage') }}" class="mt-1 block w-full" placeholder="e.g., 1500" />
                    </div>
                </div>
            </div>
        @elseif ($property_type === 'land')
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-700 mb-4">Land Features</h3>
                <div>
                    <flux:input wire:model.live="plot_size" id="plot_size" type="number" step="0.01" label="{{ __('Plot Size (sq ft)') }}" class="mt-1 block w-full" placeholder="e.g., 10000" />
                </div>
            </div>
        @endif

        <!-- Image Upload -->
        <div class="mb-6">
            <flux:heading size="lg">Property Images (Max 15)</flux:heading>
            <flux:input type="file" wire:model.live="images" multiple label="Upload Property Images" description="Select up to 15 images of your property" />
            <div wire:loading wire:target="images" class="mt-2">
                <flux:text size="sm" variant="muted">Uploading images...</flux:text>
            </div>
            @error('images.*')
                <flux:error>{{ $message }}</flux:error>
            @enderror
            @error('images')
                <flux:error>{{ $message }}</flux:error>
            @enderror

            <div class="mt-4 grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-4">
                @foreach ($images as $image)
                    @if (is_object($image) && method_exists($image, 'temporaryUrl'))
                        <img src="{{ $image->temporaryUrl() }}" class="w-full h-32 object-cover rounded-lg shadow-md" alt="Property Image Preview">
                    @endif
                @endforeach
            </div>
        </div>

        <!-- Submit Button -->
        <div class="flex items-center justify-end">
            <flux:button type="submit" variant="primary" wire:loading.attr="disabled">
                {{ __('Create Listing') }}
            </flux:button>
        </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('livewire:navigated', () => {
    if (document.getElementById('mapCreate')) {
        let map = L.map('mapCreate').setView([0, 0], 2); // Default view
        let marker;

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);

        // Function to update Livewire properties and marker
        function updateLocation(lat, lng) {
            $wire.set('latitude', lat.toFixed(7));
            $wire.set('longitude', lng.toFixed(7));

            if (marker) {
                map.removeLayer(marker);
            }
            marker = L.marker([lat, lng]).addTo(map);
            map.panTo([lat, lng]);
        }

        // Handle map click
        map.on('click', function(e) {
            updateLocation(e.latlng.lat, e.latlng.lng);
        });

        // If initial lat/lng are set, update map
        let initialLat = $wire.get('latitude');
        let initialLng = $wire.get('longitude');

        if (initialLat && initialLng && !isNaN(parseFloat(initialLat)) && !isNaN(parseFloat(initialLng))) {
            const lat = parseFloat(initialLat);
            const lng = parseFloat(initialLng);
            updateLocation(lat, lng);
            map.setView([lat, lng], 13); // Zoom in if coordinates are present
        } else {
            // Attempt to geolocate user for better default view, or use a default
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    map.setView([position.coords.latitude, position.coords.longitude], 10);
                }, function() {
                    // Geolocation failed or denied, use a wide default
                    map.setView([20, 0], 2); 
                });
            }
        }

        // Listen for changes from Livewire to update marker if lat/lng are changed manually
        Livewire.hook('element.updated', (el, component) => {
            if (component.name === 'create-property' && (el.getAttribute('wire:model.live') === 'latitude' || el.getAttribute('wire:model.live') === 'longitude')) {
                let currentLat = $wire.get('latitude');
                let currentLng = $wire.get('longitude');
                if (currentLat && currentLng && !isNaN(parseFloat(currentLat)) && !isNaN(parseFloat(currentLng))) {
                    const lat = parseFloat(currentLat);
                    const lng = parseFloat(currentLng);
                    if (marker) {
                        map.removeLayer(marker);
                    }
                    marker = L.marker([lat, lng]).addTo(map);
                    // Do not pan here to avoid interrupting manual input focus
                }
            }
        });
    }
});
</script>
@endpush
